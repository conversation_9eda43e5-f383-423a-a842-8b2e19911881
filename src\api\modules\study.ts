/**
 * 学习中心相关API接口
 * 更新时间：2025-06-22T00:38:48
 * 修复：API路径与cdcopenapi0620-2.yaml规范对齐
 */
import http from '../../utils/request';
import type { QuestionBankCategory, QuestionWithSolution, PracticeSessionRequest, PracticeSessionResult } from '../../types/api';

/**
 * 获取题库分类列表
 */
export function getQuestionCategories() {
  return http.get<QuestionBankCategory[]>('/question-bank/categories');
}

/**
 * 开始练习会话
 * @param categoryId 题库分类ID
 * @param count 题目数量，默认10题
 */
export function startPracticeSession(categoryId: string, count: number = 10) {
  const params: PracticeSessionRequest = { categoryId, count };
  return http.post<PracticeSessionResult>('/question-bank/practice', params);
}

/**
 * 获取练习题目 (兼容旧接口名称)
 * @deprecated 请使用 startPracticeSession
 */
export function getPracticeQuestions(categoryId: string, count: number = 10) {
  return startPracticeSession(categoryId, count);
}

/**
 * 获取用户练习统计
 */
export function getPracticeStats() {
  return http.get<any>('/question-bank/stats');
}
