/**
 * HTTP请求封装
 * 基于luch-request的统一请求封装
 * 更新时间：2025-06-20T11:00:39
 */
import Request from 'luch-request';
import { useUserStore } from '../stores/modules/user';
import type { ApiResponse } from '../types/api';

// 创建请求实例
const http = new Request({
  baseURL: 'http://127.0.0.1:3000/api/v1', // TODO: 替换为实际的API地址
  timeout: 10000,
  header: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    // 显示加载提示
    uni.showLoading({
      title: '加载中...',
      mask: true,
    });

    // 添加token
    const userStore = useUserStore();
    if (userStore.token) {
      config.header.Authorization = `Bearer ${userStore.token}`;
    }

    return config;
  },
  (error) => {
    uni.hideLoading();
    return Promise.reject(error);
  }
);

// 响应拦截器
http.interceptors.response.use(
  (response) => {
    uni.hideLoading();

    // 统一以 HTTP statusCode 为判断依据
    const { statusCode, data } = response as {
      statusCode: number;
      data: ApiResponse<any>;
    };

    // HTTP 状态码检查
    if (statusCode < 200 || statusCode >= 400) {
      // Token 失效
      if (statusCode === 401) {
        const userStore = useUserStore();
        userStore.clearProfile();

        uni.showToast({
          title: '登录已过期，请重新登录',
          icon: 'none',
          duration: 2000,
        });

        // 跳转到登录页
        setTimeout(() => {
          uni.reLaunch({
            url: '/pages/login/login',
          });
        }, 2000);

        return Promise.reject(data);
      }

      // 其他HTTP错误
      uni.showToast({
        title: data?.message || '请求失败',
        icon: 'none',
        duration: 2000,
      });

      return Promise.reject(data);
    }

    // HTTP 2xx/3xx 成功，检查业务状态码
    if (data && typeof data === 'object' && 'code' in data) {
      // 业务成功
      if (data.code === 200) {
        console.log('API响应成功，返回数据:', data.data);
        return Promise.resolve(data.data);
      }

      // 业务失败
      console.error('API业务失败:', data);
      uni.showToast({
        title: data.message || '操作失败',
        icon: 'none',
        duration: 2000,
      });

      return Promise.reject(data);
    }

    // 兼容性处理：如果响应不是标准包裹结构，直接返回
    // 注意：这种情况通常不应该发生，如果频繁出现需要检查API实现
    console.warn('响应数据不符合标准包裹结构，直接返回原始数据:', data);
    return Promise.resolve(data);
  },
  (error) => {
    uni.hideLoading();
    
    console.error('HTTP请求错误:', error);
    
    // 网络错误处理
    let errorMessage = '网络错误，请稍后重试';
    
    if (error.statusCode) {
      switch (error.statusCode) {
        case 404:
          errorMessage = '请求的资源不存在';
          break;
        case 500:
          errorMessage = '服务器内部错误';
          break;
        case 502:
          errorMessage = '网关错误';
          break;
        case 503:
          errorMessage = '服务不可用';
          break;
        case 504:
          errorMessage = '网关超时';
          break;
        default:
          errorMessage = `请求失败 (${error.statusCode})`;
      }
    }
    
    uni.showToast({
      title: errorMessage,
      icon: 'none',
      duration: 2000,
    });
    
    return Promise.reject(error);
  }
);

// 扩展upload方法支持文件上传
const uploadMethod = (url: string, data: Record<string, any>) => {
  return new Promise((resolve, reject) => {
    // 显示加载提示
    uni.showLoading({
      title: '上传中...',
      mask: true,
    });

    // 添加token
    const userStore = useUserStore();
    const header: Record<string, string> = {};
    if (userStore.token) {
      header.Authorization = `Bearer ${userStore.token}`;
    }

    // 处理文件上传
    const formData: Record<string, any> = {};
    const fileTempPaths: string[] = [];

    Object.keys(data).forEach(key => {
      const value = data[key];
      if (value instanceof File || (typeof value === 'object' && value.tempFilePath)) {
        // 文件对象
        fileTempPaths.push(value.tempFilePath || value);
        formData[key] = value;
      } else {
        // 普通数据
        formData[key] = value;
      }
    });

    // 如果有文件，使用uni.uploadFile
    if (fileTempPaths.length > 0) {
      uni.uploadFile({
        url: http.config.baseURL + url,
        filePath: fileTempPaths[0], // 目前只支持单文件上传
        name: Object.keys(formData).find(key => formData[key] instanceof File || (typeof formData[key] === 'object' && formData[key].tempFilePath)) || 'file',
        formData: Object.keys(formData).reduce((acc, key) => {
          if (!(formData[key] instanceof File) && !(typeof formData[key] === 'object' && formData[key].tempFilePath)) {
            acc[key] = formData[key];
          }
          return acc;
        }, {} as Record<string, any>),
        header,
        success: (res) => {
          uni.hideLoading();
          try {
            const responseData = JSON.parse(res.data);
            if (responseData.code === 200) {
              resolve(responseData.data);
            } else {
              uni.showToast({
                title: responseData.message || '上传失败',
                icon: 'none',
              });
              reject(responseData);
            }
          } catch (error) {
            uni.showToast({
              title: '响应解析失败',
              icon: 'none',
            });
            reject(error);
          }
        },
        fail: (error) => {
          uni.hideLoading();
          uni.showToast({
            title: '上传失败',
            icon: 'none',
          });
          reject(error);
        }
      });
    } else {
      // 没有文件，使用普通POST请求
      http.post(url, formData).then(resolve).catch(reject);
    }
  });
};

// 添加upload方法到http实例
(http as any).upload = uploadMethod;

export default http;
