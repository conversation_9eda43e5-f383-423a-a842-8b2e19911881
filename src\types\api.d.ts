/**
 * API相关类型定义
 * 更新时间：2025-06-22T00:38:48
 * 修复：与cdcopenapi0620-2.yaml规范完全对齐
 */

/** 通用API响应结构 - 统一包裹结构 */
export interface ApiResponse<T = any> {
  code: number;        // 业务状态码，200表示成功
  message: string;     // 响应消息
  data: T;            // 响应数据，可以是任意类型
}

/** 分页参数 */
export interface PageParams {
  page: number;
  pageSize: number;
}

/** 分页响应数据 */
export interface PageData<T> {
  items: T[];           // 数据列表，与API规范保持一致
  total: number;        // 总数量
  page: number;         // 当前页码
  pageSize: number;     // 每页数量
}

/** 分页响应 - 使用统一包裹结构 */
export interface PageResult<T> extends ApiResponse<PageData<T>> {}

// ==================== 用户相关类型 ====================

/** 机构信息 */
export interface Institution {
  id: string;
  name: string;
}

/** 职位信息 */
export interface Position {
  id: string;
  name: string;
}

/** 用户信息 */
export interface UserInfo {
  id: string;
  openid: string;
  unionid?: string;
  nickname: string;
  avatar: string;
  realName?: string;
  phone?: string;
  idCard?: string;
  organization?: string;
  position?: string;
  status: 'new' | 'pending_review' | 'approved' | 'rejected';
  token: string;
  certificateExpiry?: string;
  rejectionReason?: string; // 审核拒绝原因，当status为rejected时返回
}

/** 登录参数 */
export interface LoginParams {
  code: string;
  encryptedData?: string;
  iv?: string;
}

/** 微信登录响应数据 */
export interface WxLoginData {
  token: string;
  userStatus: 'new' | 'pending_review' | 'approved' | 'rejected';
  isVip: boolean;
  vipExpiryDate?: string;
}

/** 微信登录响应 - 使用统一包裹结构 */
export interface WxLoginResponse extends ApiResponse<WxLoginData> {}

/** 注册参数 (兼容旧版本) */
export interface RegisterParams {
  realName: string;
  phone: string;
  idCard: string;
  institutionId: string;
  positionId: string;
  avatar: string;
}

/** 个人资料提交请求 (符合API规范) */
export interface ProfileSubmissionRequest {
  name: string;
  phone: string;
  idCardNumber: string;
  photo: File | string; // 文件对象或base64字符串
  institutionId: string;
  positionId: string;
}

// ==================== 信息中心类型 ====================

/** 资讯类型 */
export type ArticleType = 'announcement' | 'policy' | 'notice';

/** 资讯文章 (符合API规范) */
export interface Article {
  id: string;
  title: string;
  content: string;
  type: ArticleType;
  isPinned: boolean;
  createdAt: string;
}

/** 信息项 (兼容旧版本) */
export interface InfoItem {
  id: string;
  title: string;
  content: string;
  summary?: string;
  type: ArticleType;
  isTop: boolean;
  publishTime: string;
  source?: string;
}

/** 信息列表参数 (兼容旧版本) */
export interface InfoListParams extends PageParams {
  type?: string;
}

// ==================== 学习中心类型 ====================

/** 题目类型 (符合API规范) */
export type QuestionType = 'single_choice' | 'multiple_choice' | 'judgment' | 'essay';

/** 题库分类 (符合API规范) */
export interface QuestionBankCategory {
  id: string;
  name: string;
  totalQuestions: number;
}

/** 题目选项 */
export interface QuestionOption {
  key: string;
  value: string;
}

/** 显示用题目 (不含答案) */
export interface QuestionForDisplay {
  id: string;
  type: QuestionType;
  stem: string;
  options?: QuestionOption[];
}

/** 带解析的题目 (含答案) */
export interface QuestionWithSolution {
  id: string;
  type: QuestionType;
  stem: string;
  options?: QuestionOption[];
  correctAnswer: string[];
  explanation?: string;
}

/** 练习会话请求 */
export interface PracticeSessionRequest {
  categoryId: string;
  count: number;
}

/** 练习会话结果 */
export interface PracticeSessionResult {
  sessionId: string;
  questions: QuestionWithSolution[];
}

/** 题库分类 (兼容旧版本) */
export interface QuestionCategory {
  id: string;
  name: string;
  description?: string;
  questionCount: number;
}

/** 题目 (兼容旧版本) */
export interface Question {
  id: string;
  type: QuestionType;
  title: string;
  options?: QuestionOption[];
  correctAnswer: string | string[];
  analysis?: string;
  categoryId: string;
}

/** 练习结果 (兼容旧版本) */
export interface PracticeResult {
  categoryId: string;
  questions: Array<{
    questionId: string;
    userAnswer: string | string[];
    isCorrect: boolean;
  }>;
  score: number;
  totalTime: number;
}

// ==================== 考试中心类型 ====================

/** 考试类型 (符合API规范) */
export type ExamType = 'online' | 'offline';

/** 考试状态 */
export type ExamStatus = 'not_started' | 'in_progress' | 'completed' | 'expired' | 'passed' | 'failed';

/** 考试 (符合API规范) */
export interface Exam {
  id: string;
  name: string;
  type: ExamType;
  status: string;
  startTime: string;
  endTime: string;
  canRetry: boolean;
}

/** 考试项 (兼容旧版本) */
export interface ExamItem {
  id: string;
  name: string;
  type: ExamType;
  status: ExamStatus;
  startTime: string;
  endTime: string;
  duration: number; // 考试时长(分钟)
  totalQuestions: number;
  passScore: number;
  currentScore?: number;
  remainingAttempts?: number;
}

/** 考试详情 */
export interface ExamDetail extends ExamItem {
  description: string;
  rules: string[];
  venues?: ExamVenue[]; // 线下考试考场信息
}

/** 考场信息 */
export interface ExamVenue {
  id: string;
  name: string;
  address: string;
  schedules: ExamSchedule[];
}

/** 考试场次 */
export interface ExamSchedule {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  capacity: number;
  registered: number;
  isBooked: boolean;
}

/** 线下考试预约 */
export interface OfflineBooking {
  bookingId: string;
  examName: string;
  venueName: string;
  startTime: string;
}

/** 考试答案 */
export interface ExamAnswer {
  questionId: string;
  answer: string | string[];
  timeSpent: number;
}

/** 人脸识别结果 */
export interface FaceVerifyResult {
  success: boolean;
  confidence: number;
  message: string;
}

// ==================== 证书相关类型 ====================

/** 证书状态 (符合API规范) */
export type CertificateStatus = 'pending_approval' | 'valid' | 'expired' | 'revoked';

/** 证书类型 */
export type CertificateType = 'qualification' | 'training' | 'skill' | 'achievement';

/** 证书信息 (符合API规范) */
export interface Certificate {
  id: string;
  name: string;
  status: CertificateStatus;
  imageUrl?: string;
  issueDate?: string;
  expiryDate?: string;
}

// ==================== 用户状态枚举 ====================

/** 用户状态 (符合API规范) */
export type UserStatus = 'new' | 'pending_review' | 'approved' | 'rejected';

// ==================== 错误响应类型 ====================

/** 错误响应 */
export interface ErrorResponse {
  code: number;
  message: string;
}
