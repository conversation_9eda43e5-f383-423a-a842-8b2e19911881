/**
 * 证书管理相关API接口
 * 更新时间：2025-06-22T00:38:48
 * 修复：API路径与cdcopenapi0620-2.yaml规范对齐
 */
import http from '../../utils/request';
import type { Certificate } from '../../types/api';

/**
 * 获取用户证书列表
 */
export function getCertificateList() {
  return http.get<Certificate[]>('/profile/certificates');
}

/**
 * 获取证书详情
 */
export function getCertificateDetail(certificateId: string) {
  return http.get<Certificate>(`/profile/certificates/${certificateId}`);
}

/**
 * 下载证书文件
 */
export function downloadCertificateFile(certificateId: string) {
  return http.get<{ downloadUrl: string }>(`/certificate/download/${certificateId}`);
}

/**
 * 申请证书续期
 */
export function renewCertificate(certificateId: string) {
  return http.post<boolean>(`/certificate/renew/${certificateId}`);
}

/**
 * 验证证书有效性
 */
export function verifyCertificate(certificateNumber: string) {
  return http.get<{ valid: boolean; certificate?: Certificate }>(`/certificate/verify/${certificateNumber}`);
}
