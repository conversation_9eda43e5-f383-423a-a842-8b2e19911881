/**
 * 信息中心相关API接口
 * 更新时间：2025-06-22T00:38:48
 * 修复：API路径与cdcopenapi0620-2.yaml规范对齐
 */
import http from '../../utils/request';
import type { Article, PageData, PageParams } from '../../types/api';

/**
 * 获取资讯列表 (统一接口)
 * @param type 资讯类型: announcement | policy | notice
 * @param params 分页参数
 */
export function getArticleList(type: 'announcement' | 'policy' | 'notice', params: PageParams) {
  return http.get<PageData<Article>>('/articles', { type, ...params });
}

/**
 * 获取公告列表
 */
export function getAnnouncementList(params: PageParams) {
  return getArticleList('announcement', params);
}

/**
 * 获取政策法规列表
 */
export function getPolicyList(params: PageParams) {
  return getArticleList('policy', params);
}

/**
 * 获取重要通知列表
 */
export function getNoticeList(params: PageParams) {
  return getArticleList('notice', params);
}

/**
 * 获取资讯详情
 * @param articleId 资讯ID
 */
export function getArticleDetail(articleId: string) {
  return http.get<Article>(`/articles/${articleId}`);
}

/**
 * 获取信息详情 (兼容旧接口名称)
 * @deprecated 请使用 getArticleDetail
 */
export function getInfoDetail(id: string) {
  return getArticleDetail(id);
}
